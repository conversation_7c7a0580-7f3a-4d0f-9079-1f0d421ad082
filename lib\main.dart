import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'ui/screens/poll_list_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final String _initialRoute = '/';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      initialRoute: _initialRoute,
      onGenerateRoute: (settings) {
        String uri = Uri.base.toString();
        if (kDebugMode) {
          print("Inside onGenerateRoute for: $uri");
        }
        if (uri.contains("pollID=")) {
          if (kDebugMode) {
            print("PollID leads to bluescreen");
          }
          return MaterialPageRoute(
            settings: settings,
            builder: (context) => const PollListScreen(),
          );
        } else {
          if (kDebugMode) {
            print("Default leads to redScreen");
          }
          return MaterialPageRoute(
            settings: settings,
            builder: (context) => const PollListScreen(),
          );
        }
      },
    );
  }
}
