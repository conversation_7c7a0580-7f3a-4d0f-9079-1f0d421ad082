import '../models/poll.dart';

class MockPollService {
  static List<Poll> getPolls() {
    return [
      Poll(
        id: '1',
        question: 'What is your favorite programming language?',
        options: ['Dart', 'Python', 'JavaScript', 'C++'],
        votes: [20, 10, 30, 40],
      ),
      Poll(
        id: '2',
        question: 'Which platform do you prefer for development?',
        options: ['Web', 'Mobile', 'Desktop', 'Embedded'],
        votes: [20, 10, 30, 40],
      ),
      Poll(
        id: '3',
        question: 'Which editor do you use the most?',
        options: ['VS Code', 'Android Studio', 'Xcode', 'Other'],
        votes: [20, 10, 30, 40],
      ),
      Poll(
        id: '4',
        question: 'Which Is President of United States?',
        options: [
          '<PERSON>',
          '<PERSON><PERSON>',
          '<PERSON>',
          '<PERSON><PERSON>'
        ],
        votes: [20, 10, 30, 40],
      ),
    ];
  }
}
