import 'package:flutter/material.dart';

class LoginPromptDialog extends StatefulWidget {
  final Function(String username, bool rememberMe) onLoginConfirmed;

  const LoginPromptDialog({
    Key? key,
    required this.onLoginConfirmed,
  }) : super(key: key);

  @override
  State<LoginPromptDialog> createState() => _LoginPromptDialogState();
}

class _LoginPromptDialogState extends State<LoginPromptDialog> {
  final TextEditingController _controller = TextEditingController();
  bool _rememberMe = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFF1F1F1F),
      title: const Text(
        "Login Required",
        style: TextStyle(color: Colors.white),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            "Please login to vote on this poll.",
            style: TextStyle(color: Colors.white70),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _controller,
            style: const TextStyle(color: Colors.white),
            decoration: const InputDecoration(
              labelText: "Username or Email",
              labelStyle: TextStyle(color: Colors.white54),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.white24),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.deepPurpleAccent),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() => _rememberMe = value ?? false);
                },
              ),
              const Text("Remember Me",
                  style: TextStyle(color: Colors.white70)),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text("Cancel", style: TextStyle(color: Colors.grey)),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.deepPurpleAccent,
            foregroundColor: Colors.white,
          ),
          onPressed: () {
            final username = _controller.text.trim();
            if (username.isNotEmpty) {
              widget.onLoginConfirmed(username, _rememberMe);
              Navigator.pop(context);
            }
          },
          child: const Text("Login"),
        ),
      ],
    );
  }
}
