import 'dart:math';
import 'package:flutter/material.dart';
import '../../models/poll.dart';

class PollCard extends StatefulWidget {
  final Poll poll;
  final VoidCallback onTap;
  final bool isLoggedIn;

  const PollCard({
    Key? key,
    required this.poll,
    required this.onTap,
    this.isLoggedIn = false,
  }) : super(key: key);

  @override
  State<PollCard> createState() => _PollCardState();
}

class _PollCardState extends State<PollCard> {
  String? selectedOption;
  late Map<String, double> votePercentages;

  @override
  void initState() {
    super.initState();
    _generateFakeVoteResults();
  }

  void _generateFakeVoteResults() {
    final random = Random();
    final votes = List.generate(
        widget.poll.options.length, (_) => random.nextInt(100) + 10);
    final total = votes.reduce((a, b) => a + b);

    votePercentages = {
      for (int i = 0; i < widget.poll.options.length; i++)
        widget.poll.options[i]: (votes[i] / total * 100)
    };
  }

  void _vote(String option) {
    if (selectedOption == null) {
      setState(() {
        selectedOption = option;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('You voted for: $option'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 193, 198, 208),
        borderRadius: BorderRadius.circular(32),
        boxShadow: const [
          BoxShadow(
            color: Color.fromARGB(25, 0, 0, 0),
            blurRadius: 16,
            offset: Offset(0, 6),
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            widget.poll.question,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
          ),
          const SizedBox(height: 32),
          ...widget.poll.options.map(
            (option) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: selectedOption == null
                  ? ElevatedButton(
                      onPressed: () {
                        if (!widget.isLoggedIn) {
                          widget.onTap();
                        } else {
                          _vote(option);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.deepPurpleAccent,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(14),
                        ),
                      ),
                      child: Text(option),
                    )
                  : _buildResultBar(option),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultBar(String option) {
    final percent = votePercentages[option] ?? 0.0;
    final isSelected = selectedOption == option;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          option,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.green : Colors.black87,
          ),
        ),
        const SizedBox(height: 6),
        Stack(
          children: [
            Container(
              height: 24,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 600),
              height: 24,
              width: MediaQuery.of(context).size.width *
                  (percent / 100) *
                  (isWide(context) ? 0.5 : 0.9),
              decoration: BoxDecoration(
                color: isSelected ? Colors.green : Colors.deepPurpleAccent,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Text('${percent.toStringAsFixed(1)}%',
            style: const TextStyle(fontSize: 12)),
        const SizedBox(height: 8),
      ],
    );
  }

  bool isWide(BuildContext context) => MediaQuery.of(context).size.width > 600;
}
